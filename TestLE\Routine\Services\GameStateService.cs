using TestLE.Routine.Interfaces;
using TestLE.Utilities;

namespace TestLE.Routine.Services;

/// <summary>
/// Single Responsibility: Manages game state queries and operations.
/// Implements IGameStateService interface following Dependency Inversion Principle.
/// </summary>
public class GameStateService : IGameStateService
{
    public bool IsPlayerDead()
    {
        var deathScreen = FindHelpers.FindDeathScreen();
        return deathScreen != null && deathScreen.isActiveAndEnabled;
    }

    public bool IsMonolithComplete()
    {
        return FindHelpers.FindMonolithCompleteButton() != null;
    }

    public bool HasObjectives()
    {
        return MONOLITH_OBJECTIVES.Count > 0;
    }

    public void ResetGameState()
    {
        ResetGlobals();
    }
}
